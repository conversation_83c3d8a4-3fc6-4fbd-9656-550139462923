import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    const body = await request.json()
    const { email, first_name, last_name, role, is_active } = body

    const updateData: any = {}
    
    if (email) {
      updateData.email = email
    }
    
    if (first_name || last_name || role) {
      updateData.user_metadata = {}
      if (first_name) updateData.user_metadata.first_name = first_name
      if (last_name) updateData.user_metadata.last_name = last_name
      if (role) updateData.user_metadata.role = role
    }

    // Gérer le statut actif/inactif
    if (typeof is_active === 'boolean') {
      if (is_active) {
        updateData.ban_duration = 'none'
      } else {
        updateData.ban_duration = '876000h' // Ban for 100 years
      }
    }

    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, updateData)

    if (error) {
      console.error('Error updating user:', error)
      return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
    }

    if (!data.user) {
      return NextResponse.json({ error: 'User update failed' }, { status: 500 })
    }

    const transformedUser = {
      id: data.user.id,
      email: data.user.email || '',
      first_name: data.user.user_metadata?.first_name || '',
      last_name: data.user.user_metadata?.last_name || '',
      role: data.user.user_metadata?.role || 'user',
      is_active: !data.user.banned_until,
      is_verified: !!data.user.email_confirmed_at,
      created_at: data.user.created_at,
      last_sign_in_at: data.user.last_sign_in_at,
      company_role: data.user.user_metadata?.role || 'user',
      user_metadata: data.user.user_metadata
    }

    return NextResponse.json(transformedUser)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    const { error } = await supabaseAdmin.auth.admin.deleteUser(userId)

    if (error) {
      console.error('Error deleting user:', error)
      return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
