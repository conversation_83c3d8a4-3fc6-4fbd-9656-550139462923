import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    const body = await request.json()
    const { is_active } = body

    const updateData = is_active 
      ? { ban_duration: 'none' }
      : { ban_duration: '876000h' } // Ban for 100 years (effectively permanent)

    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, updateData)

    if (error) {
      console.error('Error toggling user status:', error)
      return NextResponse.json({ error: 'Failed to toggle user status' }, { status: 500 })
    }

    if (!data.user) {
      return NextResponse.json({ error: 'User status toggle failed' }, { status: 500 })
    }

    const transformedUser = {
      id: data.user.id,
      email: data.user.email || '',
      first_name: data.user.user_metadata?.first_name || '',
      last_name: data.user.user_metadata?.last_name || '',
      role: data.user.user_metadata?.role || 'user',
      is_active: !data.user.banned_until,
      is_verified: !!data.user.email_confirmed_at,
      created_at: data.user.created_at,
      last_sign_in_at: data.user.last_sign_in_at,
      company_role: data.user.user_metadata?.role || 'user',
      user_metadata: data.user.user_metadata
    }

    return NextResponse.json(transformedUser)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
